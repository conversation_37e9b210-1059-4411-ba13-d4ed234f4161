from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging

from app.middleware import setup_middleware
from app.routers import agents, health, artifacts
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables from .env file
load_dotenv()
root_dir = Path(__file__).resolve().parent.parent
load_dotenv(dotenv_path=root_dir / ".env.local", override=True)
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def create_application() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI()

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, specify exact origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Setup custom middleware
    setup_middleware(app)

    # Include routers
    app.include_router(agents.router)
    app.include_router(health.router)
    app.include_router(artifacts.router)

    return app


app = create_application()
