import csv
import json
from pathlib import Path as FilePath

from fastapi import APIRouter, HTTPException, Path
from fastapi.responses import JSONResponse

router = APIRouter(prefix="/ai", tags=["artifacts"])


@router.get("/artifacts/{artifact_id}/")
async def get_artifact(
    artifact_id: str = Path(..., description="The ID of the artifact to retrieve"),
):
    """
    Retrieve an artifact by its ID.

    Returns a JSON response containing both the metadata and the artifact data.
    For chart artifacts, includes the chart configuration in the 'chart_config' field.
    For table artifacts, includes the table data as a list of dictionaries in the 'table_data' field.
    """
    # Construct the path to the artifact directory
    artifact_dir = FilePath(f"data/artifacts/{artifact_id}")

    # Check if the artifact directory exists
    if not artifact_dir.exists() or not artifact_dir.is_dir():
        raise HTTPException(
            status_code=404, detail=f"Artifact with ID {artifact_id} not found"
        )

    # Path to the metadata file
    metadata_path = artifact_dir / "metadata.json"

    # Check if metadata file exists
    if not metadata_path.exists() or not metadata_path.is_file():
        raise HTTPException(
            status_code=404, detail=f"Metadata for artifact {artifact_id} not found"
        )

    try:
        # Read and parse the metadata file
        with open(metadata_path, "r") as f:
            metadata = json.load(f)
            metadata.pop('query', None)


        # Get the artifact type from metadata
        artifact_type = metadata.get("type")

        if not artifact_type:
            raise HTTPException(
                status_code=400, detail="Invalid metadata: missing artifact type"
            )

        # Handle different artifact types
        if artifact_type.lower() == "chart":
            # Path to the chart config file
            chart_config_path = artifact_dir / "chart_config.json"

            # Check if chart config file exists
            if not chart_config_path.exists() or not chart_config_path.is_file():
                raise HTTPException(
                    status_code=404,
                    detail=f"Chart configuration for artifact {artifact_id} not found",
                )

            # Read the chart config
            with open(chart_config_path, "r") as f:
                chart_config = json.load(f)

            # Prepare response with both metadata and chart config
            response = {**metadata, "chart_config": chart_config}

            return JSONResponse(content=response)

        elif artifact_type.lower() == "table":
            # Path to the table data file
            table_data_path = artifact_dir / "table_data.csv"

            # Check if table data file exists
            if not table_data_path.exists() or not table_data_path.is_file():
                raise HTTPException(
                    status_code=404,
                    detail=f"Table data for artifact {artifact_id} not found",
                )

            # Read the CSV file content
            table_data = []
            with open(table_data_path, "r", newline="") as f:
                csv_reader = csv.DictReader(f)
                for row in csv_reader:
                    table_data.append(dict(row))

            # Prepare response with both metadata and table data
            response = {**metadata, "table_data": table_data}

            return JSONResponse(content=response)

        else:
            raise HTTPException(
                status_code=400, detail=f"Unsupported artifact type: {artifact_type}"
            )

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid metadata format")
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error retrieving artifact: {str(e)}"
        )
