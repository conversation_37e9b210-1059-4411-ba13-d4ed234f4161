from fastapi import APIRouter
import platform
from app.utils.common import is_development_mode

router = APIRouter(prefix="/health", tags=["Health"])


@router.get("/")
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "ok",
        "environment": "development" if is_development_mode() else "production",
    }


@router.get("/system/")
async def system_info():
    """System information."""
    return {
        "python_version": platform.python_version(),
        "system": platform.system(),
        "processor": platform.processor(),
    }
