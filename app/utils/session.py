from google.adk.sessions import DatabaseSessionService
from sqlalchemy import delete
from google.adk.sessions.database_session_service import StorageEvent
from typing import List
from .logger import logger


class CustomSessionService(DatabaseSessionService):
    def __init__(self, db_url: str):
        super().__init__(db_url)
        # Any additional initialization specific to CustomSessionService goes here

    async def delete_events_by_ids(
        self,
        event_ids: List[str],
        app_name: str,
        user_id: str,
        session_id: str,
    ) -> int:
        """
        Deletes events from the database based on a list of event IDs
        for a specific app, user, and session.

        Args:
            event_ids: A list of event ID strings to delete.
            app_name: The application name.
            user_id: The user ID.
            session_id: The session ID to which these events belong.

        Returns:
            The number of events deleted.
        """
        if not event_ids:
            logger.info("No event IDs provided for deletion.")
            return 0

        logger.info(
            f"Attempting to delete events with IDs: {event_ids} for app '{self}', "
            f"user '{user_id}', session '{session_id}'"
        )

        deleted_count = 0
        with self.DatabaseSessionFactory() as db_session:
            try:
                # The StorageEvent has a composite primary key including app_name, user_id, session_id, and id.
                # We need to filter by all these to correctly target the events.
                stmt = (
                    delete(StorageEvent)
                    .where(StorageEvent.id.in_(event_ids))
                    .where(StorageEvent.app_name == app_name)
                    .where(StorageEvent.user_id == user_id)
                    .where(StorageEvent.session_id == session_id)
                )
                result = db_session.execute(stmt)
                db_session.commit()
                deleted_count = result.rowcount
                logger.info(f"Successfully deleted {deleted_count} events.")
            except Exception as e:
                db_session.rollback()
                logger.error(f"Error deleting events: {e}", exc_info=True)
                raise  # Re-raise the exception to allow higher-level handling
            finally:
                # The session is automatically closed by the 'with' statement
                pass

        return deleted_count

    # --- END OF NEW METHOD ---(self, session, event):


db_url = "sqlite:///./data/session.db"
# Choose the appropriate session service based on the environment
# SessionService = (
#     InMemorySessionService() if is_development_mode() else CustomSessionService(db_url)
# )
SessionService = CustomSessionService(db_url)
