# logger.py
import logging
from rich.logging import <PERSON><PERSON>and<PERSON>
from rich.console import Console
from rich.pretty import pretty_repr
import json

# Initialize base logger
FORMAT = "%(message)s"
logging.basicConfig(
    level=logging.INFO,
    format=FORMAT,
    handlers=[RichHandler(markup=True, rich_tracebacks=True)]
)

logger = logging.getLogger("app")
console = Console()

def log_pretty(label: str, obj):
    """Pretty print complex structures (dicts/lists) with a title."""
    console.rule(f"[bold blue]{label}")
    console.print(pretty_repr(obj), soft_wrap=True)

def log_json(label: str, obj):
    """Log JSON stringified version of object with indenting."""
    try:
        json_data = json.dumps(obj, indent=2, default=str)
    except TypeError:
        json_data = str(obj)
    logger.info(f"{label}:\n{json_data}")
