from typing import Dict, Optional
from google.adk.agents import LlmAgent
from litellm import Router
import os
# Import your agents
from agents.terra.agent import get_agent as get_terra_agent
from agents.pm.agent import get_agent as get_pm_agent
from agents.forms.agent import get_agent as get_forms_agent
# Add more agents as they become available
# from agents.forms_agent.agent import forms_agent
# from agents.pm_agent.agent import pm_agent

# model_list = [{ # list of model deployments 
#     "model_name": "gpt-4.1", # model alias -> loadbalance between models with same `model_name`
#     "litellm_params": { # params for litellm completion/embedding call 
#         "model": "gpt-4.1", # actual model name
#         "api_key": os.getenv("OPENAI_API_KEY"),
#         "weight": 1
#     }
# }, {
#     "model_name": "anthropic/claude-3-7-sonnet-latest", 
#     "litellm_params": { # params for litellm completion/embedding call 
#         "model": "anthropic/claude-3-7-sonnet-latest", 
#         "api_key": os.getenv("ANTHROPIC_API_KEY"),
#         "weight": 2
#     }
# }
# ]
# router = Router(model_list=model_list, routing_strategy="cost-based-routing")
class AgentFactory:
    """Factory for creating and managing agent instances."""
    
    def __init__(self):
        self._agents: Dict[str, LlmAgent] = {
            "terra": get_terra_agent,
            "pm": get_pm_agent,
            "forms": get_forms_agent
        }
    
    def get_agent(self, metadata: str) -> Optional[LlmAgent]:
        """Get an agent by name."""
        if metadata["module_name"] == "terra":
            if metadata["mode"] == "deep_dive":
                metadata["model"] = "anthropic/claude-3-7-sonnet-latest"
            else:
                metadata["model"] = "anthropic/claude-3-5-sonnet-latest"
        return self._agents.get(metadata["module_name"])(metadata)
    

# Create a singleton instance
agents = AgentFactory()

