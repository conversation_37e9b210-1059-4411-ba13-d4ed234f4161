from pydantic import BaseModel
from typing import Dict, List, Any, Optional

class ErrorResponse(BaseModel):
    """Error response model."""
    detail: str
    
    class Config:
        schema_extra = {
            "example": {
                "detail": "Agent not found"
            }
        }

class AgentListResponse(BaseModel):
    """Response model for listing agents."""
    agents: Dict[str, str]
    
    class Config:
        schema_extra = {
            "example": {
                "agents": {
                    "terra": "Terra Agent",
                    "forms": "Forms Agent"
                }
            }
        }
