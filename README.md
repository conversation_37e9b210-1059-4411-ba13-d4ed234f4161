# Agents Backend

Multi-agent backend for TaskMapper

## Project Structure

```plaintext
agents-backend/
├── app/                  # FastAPI application
│   ├── main.py           # Entry point
│   ├── middleware.py     # Custom middleware
│   ├── routers/          # API routes
│   ├── schemas/          # Pydantic models
│   └── utils/            # Utilities and services
├── agents/               # Agent implementations
│   ├── terra/            # Terra agent
│   └── ...               # Future agent sources
└── data/                 # Data files
```

## Prerequisites

- Python 3.11 or higher
- [UV](https://docs.astral.sh/uv/getting-started/installation/) package manager

## Installation

### Install UV

Follow the installation instructions from the [official documentation](https://docs.astral.sh/uv/getting-started/installation/).

### Sync the packages

After installing UV, sync the project dependencies:

```bash
uv sync
```

## Configuration

Create a `.env.local` file in the root directory with the following variables: (This is ignored in git, so create one and keep your user specific LLM keys)

```env
# LLM API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key
OPENAI_API_KEY=your_openai_api_key
# App Configuration
DEBUG=true
```

## Running the Server

1. Running the API server to use /ai/agents/{name}/{session_id}/chat endpoint for event-streams and other endpoints:

The server can be run using the following command: (Execute from the root directory)

```bash
uv run python server.py
```

This will start the FastAPI server on [http://localhost:8001](http://localhost:8001).

2. Running the adk web:

```bash
cd agents
uv run adk web
```

This will start the Google ADK web server on [http://localhost:8000](http://localhost:8000).

