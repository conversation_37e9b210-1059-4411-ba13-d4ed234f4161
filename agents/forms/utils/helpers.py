from pathlib import Path
from fastapi import HTTPException

from .prompts import TEMPLATE_RETRIEVER_PROMPT

def construct_prompt(metadata: dict):
    """Construct the prompt for the template_retriever agent using the template ID and template schema."""
    prompt = TEMPLATE_RETRIEVER_PROMPT
    
    template_id = metadata.get("resource_id", "")
    organization_id = metadata.get("organization", "")
    project_root = Path(__file__).resolve().parents[3]
    # filename = project_root / f"data/org_id_1/forms/{template_id}/prompt_schema.txt"
    filename = project_root / f"data/{organization_id}/forms/{template_id}/prompt_schema.txt"
    
    if not filename.exists():
        raise HTTPException(
            status_code=404,
            detail=f"Schema not found for organization: {organization_id}, resource: {template_id} and module: forms",
        )
    
    schema = open(filename, "r").read()
    

    prompt = prompt.replace("<schema></schema>", schema)
    return prompt