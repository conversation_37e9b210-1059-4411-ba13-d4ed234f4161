import os
import json
import numpy as np
import pandas as pd
import duckdb
import uuid
import plotly.express as px
import plotly.graph_objects as go
import plotly
from google.adk.tools import ToolContext
from pathlib import Path
from datetime import datetime
from agents.common.utils.helpers import minimal_plotly_json


root_dir = Path(__file__).resolve().parent.parent.parent.parent


async def think(thought: str, message: str) -> dict:
    """
    Use the tool to think about database analysis and querying strategies.
    It will not execute any queries or make changes to the data, but just log the thought process.
    Use it when complex data analysis reasoning or query planning is needed.
    For example, if you need to analyze trends across multiple tables, call this tool to brainstorm
    different query approaches and assess which would be most efficient and insightful.
    Alternatively, if you're trying to identify patterns in the data, call this tool to plan out
    the analytical steps and determine the best way to structure your queries.

    Args:
        thought (str): Your thoughts about the analysis strategy.
        message (str): The user message that prompted this thought.

    Returns:
        dict: Status and content of the thinking process.
    """
    # This tool primarily serves to make the agent's reasoning explicit in the trace.
    # The return value isn't typically used directly.
    print(f"Thinking Tool Called: {thought} (User Message: {message})")
    return {"status": "success", "content": "Tool ran without errors or output."}


async def sql(
    query: str, title: str, description: str, tool_context: ToolContext
) -> dict:
    """
    Run a query on a data source. The results are always shown to the user as an artifact.
    If the user asks for all data, do not add a limit to the query.
    All results will be available to download in the artifact section.

    Args:
        query (str): The SQL query to execute.
        title (str): Title for the artifact to display.
        description (str): Description of the query's goal.
        tool_context (ToolContext): The context of the tool call, including state and other information.

    Returns:
        dict: Query results with status, content, title, description and artifact ID.
    """

    print(f"SQL Tool Called: Running query: {query} (User Message: {title})")
    try:
        request_data = tool_context.state.get("additional_request_data")
        # DATABASE_URL = root_dir / "data/org_id_1/forms/rPQUE49Hh/data.db"
        DATABASE_URL = str(root_dir / f"data/{request_data['organization']}/{request_data['module_name']}/{request_data['resource_id']}/data.db")
        con = duckdb.connect(database=DATABASE_URL, read_only=True)
        df = con.sql(query).df()
        # print(f"Query DataFrame: {df}")
        summary_df = con.sql(f"SUMMARIZE {query}").df()
        # print(f"Summary DataFrame: {summary_df}")
        con.close()

    except Exception as e:
        print(f"SQL Error: {e}")
        # Return a clear error message to the agent
        return {"status": "error", "content": f"SQL ERROR: {e}\n Try again."}

    content = ""
    # Handle empty results
    if df.empty:
        print("Query returned no results.")
        content = "The query returned no results."

    # Format the response for the agent, indicating results are displayed as an artifact
    # Provide summary and potentially truncated data back to the agent's context
    # Note: The actual display happens via the "artifact" mechanism, this return is for the agent's context
    summary_md = (
        summary_df.to_markdown(index=False)
        if not summary_df.empty
        else "Summary could not be generated."
    )

    if len(df) > 100:
        print(f"Query returned {len(df)} rows (truncated view for agent).")
        # Return truncated data and summary
        content = f"""Artifact displayed to user. User can see and download the full results ({len(df)} rows) in the artifact section.
        However, the results are truncated for you, TaskMapper (showing first 100 rows). Here is a summary:
        <records>
            {df.head(100).to_markdown(index=False)}
        </records>
        <summary>
            {summary_md}
        </summary>
        """
    else:
        print(f"Query returned {len(df)} rows.")
        # Return full data and summary
        content = f"""Artifact displayed to user. User can see and download the full results ({len(df)} rows) in the artifact section.
        <records>
            {df.to_markdown(index=False)}
        </records>
        <summary>
            {summary_md}
        </summary>
        """

    # Generate a UUID for the artifact
    artifact_id = uuid.uuid4().hex

    # Create the artifact directory
    artifact_dir = root_dir / f"data/artifacts/{artifact_id}"
    os.makedirs(artifact_dir, exist_ok=True)

    # Create metadata.json
    metadata = {
        "type": "table",
        "title": title,
        "description": description,
        "created_at": datetime.now().isoformat(),
    }

    # Save metadata.json
    with open(artifact_dir / "metadata.json", "w") as f:
        json.dump(metadata, f, indent=2)

    # Save table_data.csv
    df.to_csv(artifact_dir / "table_data.csv", index=False)

    return {
        "status": "success",
        "content": content,
        "artifact_id": artifact_id,
        "artifact_type": "table",
        "title": title,
        "description": description,
    }


async def display_chart(
    chart_code: str, title: str, description: str, query: str, tool_context: ToolContext
) -> dict:
    """Display data to the user as an artifact. Use this only after running a query to confirm the data is what you want to display. Please try to keep the chart simple and readable.
    # Known issues:
    - Addition/subtraction of integers and integer-arrays with Timestamp is no longer supported.  Instead of adding/subtracting n, use n * obj.freq
    - Plotly Express cannot process wide-form data with columns of different type.

    Args:
        chart_code (str): The code to display the artifact as a chart. This code will be run inline in a jupyter notebook cell. These imports are already available: import pandas as pd, import plotly, import plotly.express as px, import plotly.graph_objects as go, import numpy as np, import json. Use Plotly Express to create the chart. The data is already in a pandas DataFrame variable named df. Plotly code must be saved to a variable named fig. Focus on making readable charts. Carefully consider the best type of chart to use for the data. Use comments in the code to explain your reasoning. Start the code by planning your thoughts in comments. Make the chart simple and readable. Do not make it too complex. Do not set explicit width and height for the figure. Let plotly choose the best size. Try not to set explicit colors when you can use plotly's defaults like for backgrounds. A theme is already applied. use make_subplots to create multiple charts in one figure.
        title (str): Title of the chart.
        description (str): Goal of the chart in 25 words.
        query (str): Query that returned the data to display.

    Returns:
        dict: Status, content, title, description and artifact ID.
    """
    try:
        # Connect to DuckDB and execute the query
        request_data = tool_context.state.get("additional_request_data")
        # DATABASE_URL = root_dir / "data/org_id_1/forms/rPQUE49Hh/data.db"
        DATABASE_URL = str(
            root_dir
            / f"data/{request_data['organization']}/{request_data['module_name']}/{request_data['resource_id']}/data.db")
        con = duckdb.connect(database=DATABASE_URL, read_only=True)
        df = con.sql(query).df()
        con.close()
    except Exception as e:
        return f"SQL ERROR: {e}\nTry again."

    if df.empty:
        return "The query returned no results to display."

    try:
        # Make 'df' available for execution context
        global_vars = {
            "df": df,
            "px": px,
            "go": go,
            "pd": pd,
            "np": np,
            "plotly": plotly,
            "json": json,
        }
        # Evaluate the user-provided chart code
        exec(chart_code, global_vars)
        fig = global_vars.get("fig")
        if fig is None:
            return "No figure ('fig') was created in the chart code."

        # Set the title from the input parameter
        fig.update_layout(title_text=title)

        # Generate a UUID for the artifact
        artifact_id = uuid.uuid4().hex

        # Create the artifact directory
        artifact_dir = root_dir / f"data/artifacts/{artifact_id}"
        os.makedirs(artifact_dir, exist_ok=True)

        # Create metadata.json
        metadata = {
            "type": "chart",
            "title": title,
            "description": description,
            "created_at": datetime.now().isoformat(),
        }
        chart_json = minimal_plotly_json(fig)
        # Save metadata.json
        with open(artifact_dir / "metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)

        # Save chart_config.json
        with open(artifact_dir / "chart_config.json", "w") as f:
            json.dump(chart_json, f, indent=2)

        return {
            "status": "success",
            "content": "Chart displayed successfully.",
            "title": title,
            "description": description,
            "artifact_id": artifact_id,
            "artifact_type": "chart",
        }

    except Exception as e:
        return {
            "status": "error",
            "content": f"CHART EXECUTION ERROR: {e}\nCheck the chart_code syntax and try again.",
        }


async def list_forms(
    query: str, title: str, description: str, tool_context: ToolContext
) -> dict:
    """
    Run this tool only when it is necessary to render list of forms to the user on the UI.
    Run query on the data source. The results are always shown to the user as an artifact.
    If the user asks for all data, do not add a limit to the query.
    All results will be available to download in the artifact section.
    The sql query should only select the fields - ID, Form name, Asset, Due Date, Assignees

    Args:
        query (str): The SQL query to execute.
        title (str): Title for the artifact to display.
        description (str): Description of the query's goal.
        tool_context (ToolContext): The context of the tool call, including state and other information.

    Returns:
        dict: Query results with status, content, title, description and artifact ID.
    """

    print(f"SQL Tool Called: Running query: {query} (User Message: {title})")
    try:
        request_data = tool_context.state.get("additional_request_data")
        # DATABASE_URL = root_dir / "data/org_id_1/forms/rPQUE49Hh/data.db"
        DATABASE_URL = str(root_dir / f"data/{request_data['organization']}/{request_data['module_name']}/{request_data['resource_id']}/data.db")
        con = duckdb.connect(database=DATABASE_URL, read_only=True)
        df = con.sql(query).df()
        summary_df = con.sql(f"SUMMARIZE {query}").df()
        con.close()

    except Exception as e:
        print(f"SQL Error: {e}")
        # Return a clear error message to the agent
        return {"status": "error", "content": f"SQL ERROR: {e}\n Try again."}

    content = ""
    # Handle empty results
    if df.empty:
        print("Query returned no results.")
        content = "The query returned no results."

    # Format the response for the agent, indicating results are displayed as an artifact
    # Provide summary and potentially truncated data back to the agent's context
    # Note: The actual display happens via the "artifact" mechanism, this return is for the agent's context
    summary_md = (
        summary_df.to_markdown(index=False)
        if not summary_df.empty
        else "Summary could not be generated."
    )

    if len(df) > 100:
        print(f"Query returned {len(df)} rows (truncated view for agent).")
        # Return truncated data and summary
        content = f"""Artifact displayed to user. User can see and download the full results ({len(df)} rows) in the artifact section.
        However, the results are truncated for you, TaskMapper (showing first 100 rows). Here is a summary:
        <records>
            {df.head(100).to_markdown(index=False)}
        </records>
        <summary>
            {summary_md}
        </summary>
        """
    else:
        print(f"Query returned {len(df)} rows.")
        # Return full data and summary
        content = f"""Artifact displayed to user. User can see and download the full results ({len(df)} rows) in the artifact section.
        <records>
            {df.to_markdown(index=False)}
        </records>
        <summary>
            {summary_md}
        </summary>
        """

    # Generate a UUID for the artifact
    artifact_id = uuid.uuid4().hex

    # Create the artifact directory
    artifact_dir = root_dir / f"data/artifacts/{artifact_id}"
    os.makedirs(artifact_dir, exist_ok=True)

    # Create metadata.json
    metadata = {
        "type": "table",
        "title": title,
        "description": description,
        "created_at": datetime.now().isoformat(),
    }

    # Save metadata.json
    with open(artifact_dir / "metadata.json", "w") as f:
        json.dump(metadata, f, indent=2)

    # Save table_data.csv
    df.to_csv(artifact_dir / "table_data.csv", index=False)

    return {
        "status": "success",
        "content": content,
        "artifact_id": artifact_id,
        "artifact_type": "forms_list",
        "title": title,
        "description": description,
    }
