import litellm
from google.adk.sessions import Session
import json


def get_related_questions(session: Session, module_name: str) -> dict:
    """
    Generates a list of related questions based on conversation history,
    session state, and recent tool actions, requesting JSON output from LiteLLM.
    Corrected to match the provided Event object structure.

    Args:
        session: The session object containing conversation history and state.

    Returns:
        A dictionary with status and a list of strings, where each string
        is a suggested related question.
    """

    print("Generating related questions for session:", session.id)

    # --- Configuration ---
    LLM_MODEL_NAME = "gpt-4.1"  # Or your preferred LiteLLM model name
    N_PAST_EVENTS = 5  # Number of recent events to include in history context

    # --- Extract Conversation History ---
    recent_events = session.events[-N_PAST_EVENTS:]  # Get the last N events

    # Import the appropriate prompt based on module name
    suggestion_instructions = """
    Additionally, analyze the conversation and suggest 3 relevant follow-up questions.
    Generate exactly 3 distinct questions that a user might logically ask next.
    The questions should be directly related to the current conversation but not repeat what's already been answered.
    Questions should not be suggestive in a third person way, it should be a first person way.
    Your response for this part must be a JSON object with a single key "related_questions" containing an array of 3 question strings.
    Ignore all the tool calls in the system prompt.
    """

    # Get base prompt based on module name
    if module_name == "pm":
        from agents.pm.utils.prompts import get_prompt

        system_prompt = get_prompt({}) + suggestion_instructions
    elif module_name == "terra":
        from agents.terra.utils.prompts import PROGRESS_DATA_PROMPT_2

        system_prompt = PROGRESS_DATA_PROMPT_2 + suggestion_instructions
    elif module_name == "forms":
        # For forms, we need to check if it's template_retriever or not
        resource_type = session.state.get("additional_request_data", {}).get(
            "resource_type", ""
        )
        resource_id = session.state.get("additional_request_data", {}).get(
            "resource_id", ""
        )
        data = session.state.get("additional_request_data", {}).get(
            "data", ""
        )

        if resource_type == "template_retriever" and resource_id:
            from agents.forms.utils.helpers import construct_prompt

            metadata = {
                "module_name": module_name,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "data": data
            }
            system_prompt = construct_prompt(metadata) + suggestion_instructions
        else:
            from agents.forms.utils.prompts import TEMPLATE_CREATION_PROMPT

            system_prompt = TEMPLATE_CREATION_PROMPT + suggestion_instructions

    # Only include the last 3 user messages for context
    recent_user_messages = []
    for event in reversed(recent_events):
        role = getattr(
            getattr(getattr(event, "content", None), "role", None), "value", None
        )
        if role is None:
            role = getattr(getattr(event, "content", None), "role", "Unknown")

        if role.lower() == "user":
            part = getattr(getattr(event, "content", None), "parts", [None])[0]
            if part and hasattr(part, "text") and part.text:
                recent_user_messages.append(part.text)
                if len(recent_user_messages) >= 3:
                    break

    user_context = (
        "Based on these recent user messages, suggest follow-up questions:\n\n"
    )
    for i, msg in enumerate(reversed(recent_user_messages)):
        user_context += f"Message {i + 1}: {msg}\n\n"

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_context},
    ]

    print("messages", messages)
    # --- Call the LLM using litellm.completion ---
    try:
        litellm_params = {
            "model": LLM_MODEL_NAME,
            "messages": messages,
            "temperature": 0.5,
            "response_format": {"type": "json_object"},
        }

        response = litellm.completion(**litellm_params)

        # --- Parse JSON Response ---
        questions = []
        if (
            response
            and response.choices
            and response.choices[0].message
            and response.choices[0].message.content
        ):
            generated_text = response.choices[0].message.content
            try:
                # Attempt to parse the entire response content as JSON
                parsed_json = json.loads(generated_text)

                # Extract the list of questions from the JSON object
                if (
                    isinstance(parsed_json, dict)
                    and "related_questions" in parsed_json
                    and isinstance(parsed_json["related_questions"], list)
                ):
                    # Filter to ensure items are strings and take up to 3
                    questions = [
                        q
                        for q in parsed_json["related_questions"]
                        if isinstance(q, str)
                    ][:3]
                    print(
                        f"Successfully parsed JSON and extracted {len(questions)} questions."
                    )
                else:
                    print("JSON response does not match expected structure.")
                    # If JSON is valid but structure is wrong, return a specific error or empty list
                    return {
                        "status": "failed",
                        "content": "LLM generated invalid JSON structure.",
                    }

            except json.JSONDecodeError:
                print(
                    f"LLM response was not valid JSON. Raw response: {generated_text}"
                )
                # Handle cases where the LLM fails to produce valid JSON
                return {
                    "status": "failed",
                    "content": "LLM failed to generate valid JSON.",
                }
            except Exception as e:
                print(f"An unexpected error occurred during JSON parsing: {e}")
                return {"status": "failed", "content": f"Unexpected parsing error: {e}"}

            # Return the extracted questions
            return {"status": "success", "content": questions}

        else:
            print("LLM response candidates missing or empty.")
            return {
                "status": "failed",
                "content": "LLM response candidates missing or empty.",
            }

    except Exception as e:
        print(f"Error during LiteLLM completion call: {e}")
        # Consider logging the specific LiteLLM error type and details if needed
        return {"status": "failed", "content": f"Error calling LLM: {e}"}
