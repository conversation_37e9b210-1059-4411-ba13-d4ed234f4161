<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Plotly Chart JSON Viewer</title>
  <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
  <style>
    body {
      margin: 0;
      display: flex;
      height: 100vh;
      font-family: Arial, sans-serif;
    }
    textarea {
      width: 40%;
      padding: 1rem;
      border: none;
      border-right: 1px solid #ccc;
      resize: none;
      font-family: monospace;
      font-size: 14px;
    }
    #chart {
      flex: 1;
      padding: 1rem;
    }
  </style>
</head>
<body>

  <textarea id="jsonInput" placeholder="Paste your Plotly chart JSON here...">{ "data": [ { "x": [1, 2, 3], "y": [2, 6, 3], "type": "scatter", "mode": "lines+markers", "marker": { "color": "red" } } ], "layout": { "title": "Sample Chart" } }</textarea>

  <div id="chart"></div>

  <script>
    const input = document.getElementById('jsonInput');
    const chart = document.getElementById('chart');

    function updateChartFromJSON() {
      try {
        const parsed = JSON.parse(input.value);
        Plotly.newPlot(chart, parsed.data, parsed.layout || {});
      } catch (err) {
        chart.innerHTML = '<p style="color:red;">Invalid JSON</p>';
      }
    }

    input.addEventListener('input', () => {
      updateChartFromJSON();
    });

    // Initial render
    updateChartFromJSON();
  </script>

</body>
</html>