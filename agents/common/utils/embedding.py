import os
import requests


def embed(input_text: str) -> list[float]:
    """Method take a activity_name as an input and return embeddings.
    Args:
        activity_name (str): Activity name.
    Returns:
        list[float]: Activity embedding.
    """

    response = requests.post("https://api.deepinfra.com/v1/openai/embeddings", headers={
            "Authorization": f"Bearer {os.getenv('DEEPINFRA_API_KEY')}",
        }, json={
            "input": input_text,
            "model": "BAAI/bge-base-en-v1.5",
            "encoding_format": "float"
        })

    return response.json().get("data", [{}])[0].get("embedding")
