import json
import os
import uuid
from pathlib import Path
from datetime import datetime
from typing import Any, Optional

import pandas as pd

# Root directory (4 levels up from this file)
root_dir = Path(__file__).resolve().parent.parent.parent.parent


def get_uid() -> str:
    """Generate a unique Artifact ID using UUID4.
    Returns:
        str: A unique hexadecimal string.
    """
    return uuid.uuid4().hex


def get_meta_data(type: str, title: str, description: str, query: str) -> dict:
    """Generate a table metadata dictionary.
    Args:
        type (str): Type of the artifact.(table, chart, etc.) default is table.
        title (str): Title of the table.
        description (str): Description of the table.
        query (str): The query used to generate the table.
    Returns:
        dict: Table metadata dictionary.
    """
    return {
        "type": type or "table",
        "title": title,
        "description": description,
        "query": " ".join(query.replace("\n", " ").split()) if query else None,
        "created_at": datetime.now().isoformat(),
    }


def create_artifact(agent_name: str, file_name: str, data: Any, uid: Optional[str] = None) -> str:
    """Create and save an artifact under a specific agent directory.
    Args:
        agent_name (str): The name of the agent (e.g., 'terra', 'pm', 'forms').
        file_name (str): Name of the file (must end with .txt, .csv, or .json).
        data (Any): The content to save. Must match the file type:
                    - pd.DataFrame for .csv
                    - dict for .json
                    - str for .txt
        uid (Optional[str]): An optional unique ID. If not provided, a new one is generated.
    Returns:
        str: The UID of the created artifact.
    Raises:
        ValueError: If inputs are invalid or unsupported file type/data combination.
    """
    # Validate agent name
    if agent_name not in {"terra", "pm", "forms"}:
        raise ValueError(f"Invalid agent name: {agent_name}")

    # Validate file name
    if not (file_name and file_name.endswith((".txt", ".csv", ".json"))):
        raise ValueError("Invalid file name. Must end with .txt, .csv, or .json")

    if data is None:
        raise ValueError("Data is required")

    # Generate UID if not provided
    uid = uid or get_uid()

    # Create artifact directory
    # *imp* agent_name will be added to the path later.
    # artifact_dir = root_dir / f"data/artifacts/{agent_name}/{uid}" 
    artifact_dir = root_dir / f"data/artifacts/{uid}" 
    os.makedirs(artifact_dir, exist_ok=True)
    file_path = artifact_dir / file_name

    # Write data based on file type
    if file_name.endswith(".csv") and isinstance(data, pd.DataFrame):
        data.to_csv(file_path, index=False)
    elif file_name.endswith(".json"):
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4)
    elif file_name.endswith(".txt"):
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(str(data))
    else:
        raise ValueError("Invalid file type or mismatched data type for file")

    # TBD: Optionally upload to S3 or another storage location

    return uid
