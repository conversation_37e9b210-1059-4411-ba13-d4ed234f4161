import json

import plotly


def minimal_plotly_json(fig):
    """
    Returns a minimal JSON string from a Plotly figure,
    stripping unnecessary default fields from layout.
    """

    def strip_layout_defaults(layout):
        layout_dict = layout.to_plotly_json()
        # Remove common default-heavy keys
        for key in ["template", "uirevision", "meta", "font", "modebar"]:
            layout_dict.pop(key, None)
        return layout_dict

    # Extract minimal representation
    minimal_dict = {
        "data": [trace.to_plotly_json() for trace in fig.data],
        "layout": strip_layout_defaults(fig.layout),
    }

    return json.dumps(minimal_dict, cls=plotly.utils.PlotlyJSONEncoder)
