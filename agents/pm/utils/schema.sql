-- Table: resources
-- Defines resource pools available for assignment to activities.
CREATE TABLE IF NOT EXISTS resources (
	id VARCHAR, -- Unique identifier for the resource
	name VARCHAR, -- Name of the resource
	type VARCHAR, -- Resource type (e.g., member, custom(equipment, material))
	member_id VARCHAR, -- User UID in case of a 'member' otherwise null.
	cost_type ENUM('per_hour', 'per_item', 'fixed'), -- Cost classification (per_hour, per_item, fixed)
	cost DOUBLE, -- Cost per unit, per hour
	PRIMARY KEY(id),
);
-- Table: activities
-- Activities represent individual tasks or deliverables within a schedule. One schedule can have multiple activities.
-- Activities can be of four types: PROJECT, WBS, TASK, and MILESTONE, identified by 'type' and 'is_milestone' columns.
-- 'PROJECT' is the root activity, 'WBS' is an intermediate activity, and 'TASK' and 'MILESTONE' are leaf activities.
-- Activities are linked hierarchically via the 'parent' column and the entire hierarchy is traceable via 'wbs_path' (IDs).
-- Sometimes "task" and "activity" are used interchangeably; in such cases, prefer the activity with type 'TASK'.
CREATE TABLE IF NOT EXISTS activities (
	id VARCHAR, -- ID of the activity; can be the same across different schedules, hence 'uid' is better for unique identification
	parent VARCHAR, -- Parent activity ID (for WBS hierarchy); NULL for the root activity
	wbs_path VARCHAR, -- WBS ids path: comma-separated activity IDs (e.g., ",parent,wbs,...,activity,")
	name VARCHAR, -- Name/description of the activity
	type ENUM('PROJECT', 'WBS', 'TASK'), -- Type of activity
	planned_start DATE, -- Updated planned start date
	planned_finish DATE, -- Updated planned finish date
	actual_start DATE, -- Actual start date
	actual_finish DATE, -- Actual finish date
	data_date DATE, -- Date of progress report
	is_critical BOOLEAN DEFAULT FALSE, -- Whether the activity is critical (affects project end date)
	is_milestone BOOLEAN DEFAULT FALSE, -- Whether the activity is a milestone
	planned_duration DOUBLE, -- Updated planned duration (days)
	actual_duration DOUBLE, -- Actual duration (days)
	remaining_duration DOUBLE, -- Remaining duration to complete (days)
	planned_percentage DOUBLE DEFAULT 0, -- Percent of schedule work completed
	actual_percentage DOUBLE DEFAULT 0, -- Current progress percentage
	remaining_percentage DOUBLE DEFAULT 0, -- Current progress percentage
	free_slack DOUBLE DEFAULT 0, -- Free slack available (delay without affecting successors)
	total_slack DOUBLE DEFAULT 0, -- Total slack available
	planned_cost DOUBLE, -- Planned cost
	actual_cost DOUBLE, -- Actual cost incurred
	remaining_cost DOUBLE, -- Remaining cost to complete
	required_work_rate DOUBLE, -- Required Work rate (units per day)
	planned_work DOUBLE, -- Planned work effort (hours)
	actual_work DOUBLE, -- Actual work effort completed (hours)
	remaining_work DOUBLE, -- Remaining work effort (hours)
	planned_work_rate DOUBLE, -- Planned work rate
	actual_work_rate DOUBLE, -- Actual work rate
	remaining_work_rate DOUBLE, -- Remaining work rate
	overall_weight DOUBLE DEFAULT 0, -- Overall weight/importance of the activity
	weight DOUBLE DEFAULT 0, -- Weight in the parent WBS progress calculation
	predecessors VARCHAR, -- Predecessor activities
	status ENUM('Not Started', 'On Track', 'Delayed', 'Completed'), -- Status of the activity (e.g., Not Started, In Progress, Complete)
	priority ENUM('High', 'Medium', 'Low'), -- Activity priority
	category VARCHAR, -- Activity category
	tags VARCHAR, -- Comma separated Activity tags
	cpi DOUBLE, -- Cost Performance Index
	spi DOUBLE, -- Schedule Performance Index
	earned_value DOUBLE, -- Earned Value
	planned_value DOUBLE, -- Planned Value
	schedule_variance DOUBLE, -- Schedule Variance
	cost_variance DOUBLE, -- Cost Variance
	created_at DATE, -- Creation timestamp
	last_updated_at DATE, -- Last updated timestamp
	PRIMARY KEY(id),
	UNIQUE (wbs_path),
);
-- activities indexes
CREATE INDEX IF NOT EXISTS idx_activities_type ON activities(type);
CREATE INDEX IF NOT EXISTS idx_activities_wbs_path ON activities(wbs_path);
-- Table: custom_fields
-- Defines custom field templates that can be attached to activities.
CREATE TABLE IF NOT EXISTS custom_fields (
	activity VARCHAR, -- Associated activity UID
	name VARCHAR, -- Name of the custom field
	value VARCHAR, -- Value of custom field
	type VARCHAR, -- Type of custom field (e.g., text, number, date, money etc.)
	PRIMARY KEY(activity, name),
	FOREIGN KEY (activity) REFERENCES activities(id)
);
-- Table: resource_assignments
-- Assignment of resources to specific activities.
CREATE TABLE IF NOT EXISTS resource_assignments (
	activity VARCHAR, -- Linked activity ID
	resource VARCHAR, -- Linked resource ID
	planned_units DOUBLE, -- Total work assigned (hours, items, cost(when resource type is fixed))
	actual_units DOUBLE, -- Total work done (hours, items)
	planned_cost DOUBLE, -- Cost incurred based on utilization
	actual_cost DOUBLE, -- Cost incurred based on utilization
	PRIMARY KEY(activity, resource),
	FOREIGN KEY (activity) REFERENCES activities(id),
	FOREIGN KEY (resource) REFERENCES resources(id)
);
-- Table: scheduled_progress_completions
-- Captures planned progress (baseline) completion percentages at specific dates for activities.
CREATE TABLE IF NOT EXISTS scheduled_progress_completions (
	activity VARCHAR, -- Associated activity ID
	date DATE, -- Planned reporting date
	planned_percentage DOUBLE, -- Planned percent complete (0-1)
	PRIMARY KEY(activity, date),
	FOREIGN KEY (activity) REFERENCES activities(id)
);
-- Table: actual_progress_completions
-- Records actual progress updates for activities on different dates.
CREATE TABLE IF NOT EXISTS actual_progress_completions (
	activity VARCHAR, -- Associated activity ID
	date DATE, -- Actual reporting date
	actual_percentage DOUBLE, -- Actual progress (0-1)
	PRIMARY KEY(activity, date),
	FOREIGN KEY (activity) REFERENCES activities(id)
);
-- Table: actual_cost_histories
-- Historical tracking of actual cost updates for activities.
CREATE TABLE IF NOT EXISTS actual_cost_histories (
	activity VARCHAR, -- Associated activity ID
	date DATE, -- Reporting date
	actual_cost DOUBLE, -- actual cost on any date
	PRIMARY KEY(activity, date),
	FOREIGN KEY (activity) REFERENCES activities(id)
);
-- Table: actual_work_histories
-- Historical tracking of actual work (effort) updates for activities.
CREATE TABLE IF NOT EXISTS actual_work_histories (
	activity VARCHAR, -- Associated activity ID
	date DATE, -- Reporting date
	actual_work DOUBLE, -- actual work (hours) on any date
	PRIMARY KEY(activity, date),
	FOREIGN KEY (activity) REFERENCES activities(id)
);