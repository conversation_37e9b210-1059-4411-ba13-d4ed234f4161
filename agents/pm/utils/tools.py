import json
import plotly
import duckdb
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from pathlib import Path
from google.adk.tools import ToolContext
from agents.common.utils import embedding as em
from agents.common.utils import artifacts as af
from agents.common.utils.helpers import minimal_plotly_json


pd.set_option("display.max_rows", 100)
pd.set_option('display.max_colwidth', None)  # or a large number like 1000
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

root_dir = Path(__file__).resolve().parent.parent.parent.parent


def get_db_connection(organization_uid: str, schedule_uid: str) -> duckdb.DuckDBPyConnection:
    db_path = "/".join([str(root_dir), f"data/{organization_uid}/pm/schedule/{schedule_uid}/data.db"])
    con = duckdb.connect(database=db_path, read_only=False)
    return con


def think(thought: str, title: str, description: str) -> dict:
    """Tool to brainstorm query strategies or analysis.
    Args:
        thought (str): Your thoughts about the analysis strategy.
        title (str): Title for the thought.
        description (str): Description of the thought in 25 words.
    Use cases:
        Plan and reflect on complex SQL query logic, analysis strategies, or ambiguous user requests.
        Use this tool to log internal thought processes and brainstorming related to data analysis,
        such as identifying trends, determining which tables/columns to query, or evaluating
        different strategies. It does not execute SQL or return data.
    Instructions:
        - You need to disambiguate or complete a partial activity name, before providing it to **reference** tool.
        - You want to retrieve related activities or their metadata for further querying.
        - Results are shown as artifacts to the user.
        - If the user requests "all data", do not apply any result limit.
    Returns:
        dict: Status and result of thinking process.
    """
    return {
        "status": "success",
        "result": "Tool ran without errors or output."
    }


def activity_finder(activity_name: str, title: str, description: str, n_results: int, tool_context: ToolContext) -> dict:
    """Tool to retrieve similar activity documents and metadata.
    Args:
        activity_name (str): Partial/vogue activity name for similarity search.
        title (str): Title for the artifact to display.
        description (str): Description of the query's goal in 25 words.
        n_results (int): Number of relevant results to retrieve. Default is 5.
    Returns:
        dict: status and result of similar activities with metadata or error message.
    """
    try:
        embeddings = em.embed(activity_name)
        embeddings = ",".join([str(v) for v in embeddings])
        query = f'SELECT activity, text, wbs_path FROM activity_embeddings ORDER BY array_cosine_distance(embedding, [{embeddings}]::FLOAT[768]) ASC LIMIT {n_results};'
        request_data = tool_context.state.get("additional_request_data", {})
        organization_uid = request_data.get("organization", "org_id_1")
        schedule_uid = request_data.get("resource_uid", "schedule_id_1")
        con = get_db_connection(organization_uid, schedule_uid)
        df = con.sql(query).df()
        con.close()
        return {
            "status": "success",
            "result": f"""Reference results:
                <records>
                {df}
                </records>
            """
        }
    except Exception as e:
        return {
            "status": "error",
            "error": f"Reference Error: {e}\n Try again with same query."
        }


def sql(query: str, title: str, description: str, tool_context: ToolContext) -> dict:
    """Run a SQL query on DuckDB and return artifact-ready output.
    Args:
        query (str): The SQL query to execute.
        title (str): Title for the artifact to display.
        description (str): Description of the query's goal in 25 words.
    Returns:
        dict: Query results with status, content, title, description and artifact ID.
    """
    try:
        request_data = tool_context.state.get("additional_request_data", {})
        organization_uid = request_data.get("organization", "org_id_1")
        schedule_uid = request_data.get("resource_uid", "schedule_id_1")
        con = get_db_connection(organization_uid, schedule_uid)
        df = con.sql(query).df()
        summary_df = con.sql(f"SUMMARIZE {query}").df()
        con.close()
    except Exception as e:
        return {
            "status": "error",
            "content": f"SQL ERROR: {e}\n Try again."
        }

    content = ""

    if len(df) > 100:
        content = f"""Artifact displayed to user. Results truncated (over 100 rows).
            <records>
            {df}
            </records>
            <summary>
            {summary_df}
            </summary>
        """
    elif df.empty:
        content = "The query returned no results."

    content = f"""Artifact displayed to user.
        <records>
        {df}
        </records>
    """

    # Save the artifact
    artifact_uid = af.get_uid()
    metadata = af.get_meta_data("table", title, description, query)
    af.create_artifact("pm", "metadata.json", metadata, artifact_uid)
    af.create_artifact("pm", "table_data.csv", df, artifact_uid)

    return {
        "status": "success",
        "content": content,
        "artifact_id": artifact_uid,
        "artifact_type": "table",
        "title": title,
        "description": description,
    }


async def display_chart(chart_code: str, title: str, description: str, query: str, tool_context: ToolContext) -> dict:
    """Display data to the user as an artifact. Use this only after running a query to confirm the data is what you want to display. Please try to keep the chart simple and readable.
    # Known issues:
    - Addition/subtraction of integers and integer-arrays with Timestamp is no longer supported.  Instead of adding/subtracting n, use n * obj.freq
    - Plotly Express cannot process wide-form data with columns of different type.
    Args:
        chart_code (str): The code to display the artifact as a chart. This code will be run inline in a jupyter notebook cell. These imports are already available: import pandas as pd, import plotly, import plotly.express as px, import plotly.graph_objects as go, import numpy as np, import json. Use Plotly Express to create the chart. The data is already in a pandas DataFrame variable named df. Plotly code must be saved to a variable named fig. Focus on making readable charts. Carefully consider the best type of chart to use for the data. Use comments in the code to explain your reasoning. Start the code by planning your thoughts in comments. Make the chart simple and readable. Do not make it too complex. Do not set explicit width and height for the figure. Let plotly choose the best size. Try not to set explicit colors when you can use plotly's defaults like for backgrounds. A theme is already applied. use make_subplots to create multiple charts in one figure.
        title (str): Title of the chart.
        description (str): Goal of the chart in 25 words.
        query (str): Query that returned the data to display.
    Returns:
        dict: Status, content, title, description and artifact ID.
    """
    try:
        request_data = tool_context.state.get("additional_request_data", {})
        organization_uid = request_data.get("organization", "org_id_1")
        schedule_uid = request_data.get("resource_uid", "schedule_id_1")
        con = get_db_connection(organization_uid, schedule_uid)
        df = con.sql(query).df()
        con.close()
    except Exception as e:
        return {
            "status": "error",
            "content": f"SQL ERROR: {e}\nTry again."
        }

    if df.empty:
        return {
            "status": "error",
            "content": "The query returned no results to display."
        }

    try:
        global_vars = {
            "df": df,
            "px": px,
            "go": go,
            "pd": pd,
            "np": np,
            "plotly": plotly,
            "json": json,
        }

        exec(chart_code, global_vars)
        fig = global_vars.get("fig")
        if fig is None:
            return {
                "status": "error",
                "content": "No figure ('fig') was created in the chart code."
            }

        fig.update_layout(title_text=title)
        chart_json = minimal_plotly_json(fig)

        # Save the artifact
        artifact_uid = af.get_uid()
        metadata = af.get_meta_data("chart", title, description, query)
        af.create_artifact("pm", "metadata.json", metadata, artifact_uid)
        af.create_artifact("pm", "chart_config.json", chart_json, artifact_uid)
        # for chart re-creation and pin.
        af.create_artifact("pm", "chart_code.txt", chart_code, artifact_uid)

        return {
            "status": "success",
            "content": "Chart displayed successfully.",
            "title": title,
            "description": description,
            "artifact_id": artifact_uid,
            "artifact_type": "chart",
        }

    except Exception as e:
        return {
            "status": "error",
            "content": f"CHART EXECUTION ERROR: {e}\nCheck the chart_code syntax and try again.",
        }
