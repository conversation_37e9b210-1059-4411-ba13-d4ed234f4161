from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmResponse, LlmRequest
from typing import Optional
from .utils.tools import think, sql, display_chart
from .utils.prompts import SIMPLIFIED_PROGRESS_DATA_PROMPT
from dotenv import load_dotenv
from pathlib import Path
from app.utils.logger import log_pretty

# Load environment variables from .env and .env.local file
load_dotenv()
root_dir = Path(__file__).resolve().parent.parent.parent
load_dotenv(dotenv_path=root_dir / ".env.local", override=True)


def before_model_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    log_pretty("Before Model Request", llm_request)
    return None


def get_agent(metadata):
    ## metadata will have resource_type and resource_id as well to decide which agent to use if multi agent in the same module, and dynamic prompts
    config = {
        "name": "terra_agent",
        "model": LiteLlm(
            model=metadata["model"],
            parallel_tool_calls=False,
            drop_params=True,
            temperature=0,
            stream_options={"include_usage": True},
        ),
        "instruction": SIMPLIFIED_PROGRESS_DATA_PROMPT,
        "tools": [think, sql, display_chart],
    }
    return LlmAgent(**config)


## This is only for adk web as it looks for root_agent
root_agent = get_agent({"model": "anthropic/claude-3-7-sonnet-latest"})
