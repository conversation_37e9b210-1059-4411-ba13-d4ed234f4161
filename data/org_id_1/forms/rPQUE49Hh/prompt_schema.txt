* Table Name: forms
* **"ID"** (VARCHAR)
    * Samples: `['Ma4mJkeGVqE', 'FU7Ic2P0iZA', 'TvjMw4qUX9K']`
* **"Form name"** (VARCHAR)
    * Samples: `['VIS2021 - TALISAY', 'BCD234 I&C Workflow v4.0', 'ABC123 I&C Workflow v4.0']`
* **"Submitted by"** (VARCHAR)
    * Samples: `['-', '<PERSON><PERSON>', 'zjaneth-relucio micropowerph']`
* **"Submitted at"** (TIMESTAMP)
    * Samples: `[2024-12-19 06:54:00, 2024-12-29 06:54:00]`
* **"Attached to"** (VARCHAR)
    * Samples: `['-', 'BCD234', 'ABC123']`
* **"Reason"** (VARCHAR)
    * Samples: `['-', 'Test']`
* **"Status"** (VARCHAR)
    * Samples: `['1.0 Vendor Allocation', 'Submitted', '5.4 SKOM Validation']`
* **"Asset"** (VARCHAR)
    * Samples: `['Project Helios [1000 Globe Sites]', 'Demo', '-']`
* **"1.0 Vendor Allocation__1.3 I&C Vendor"** (VARCHAR)
    * Samples: `['-', 'Joemar Rivera', 'arnt.min.thu micropowerph']`
* **"1.0 Vendor Allocation__1.5 Region"** (VARCHAR)
    * Samples: `['-', 'Mindanao (MIN)', 'South Luzon (SLZ)']`
* **"1.0 Vendor Allocation__1.1 MPP Project Coordinator"** (VARCHAR)
    * Samples: `['-', 'zjaneth-relucio micropowerph', 'zkathrina-sanpedro micropowerph']`
* **"1.0 Vendor Allocation__1.2 MPP Field Engineer"** (VARCHAR)
    * Samples: `['-', 'erick-acob micropowerph', 'Izle Mae Perez']`
* **"1.0 Vendor Allocation__1.4 Project Manager"** (VARCHAR)
    * Samples: `['-', 'anand-borade micropowerph', 'zkathrina-sanpedro micropowerph']`
* **"1.0 Vendor Allocation__1.7 QHSE"** (VARCHAR)
    * Samples: `['-', 'ruel-bejo micropowerph', 'gil.iraola micropowerph']`
* **"1.0 Vendor Allocation__1.8 WH Supervisor"** (VARCHAR)
    * Samples: `['-', 'francis-cachero micropowerph', 'CAMILLE MANALO']`
* **"1.0 Vendor Allocation__1.6 Regional I&C Manager"** (VARCHAR)
    * Samples: `['-', 'melchor-albino micropowerph', 'ruel-bejo micropowerph']`
* **"2.0 Project Manager Approval__2.1 Vendor Assignment Approved?"** (VARCHAR)
    * Samples: `['-', 'Yes']`
* **"3.0 Supervisor Allocation__3.1 Supervisor Name"** (VARCHAR)
    * Samples: `['-', 'Joemar Rivera', 'ABC']`
* **"3.0 Supervisor Allocation__3.2 Supervisor Email"** (VARCHAR)
    * Samples: `['-', '<EMAIL>', '<EMAIL>']`
* **"3.0 Supervisor Allocation__3.3 Supervisor Phone"** (VARCHAR)
    * Samples: `['-', '639086216898', '639171351757']`
* **"3.0 Supervisor Allocation__3.4 Remarks"** (VARCHAR)
    * Samples: `['-', 'OK', 'Testing XYZ321 I&C']`
* **"4.0 Supervisor Onboarding__4.1 Supervisor Assigned"** (VARCHAR)
    * Samples: `['-', 'Joemar Rivera', 'richard-cadungog micropowerph']`
* **"4.0 Supervisor Onboarding__4.2 Message to Supervisor"** (VARCHAR)
    * Samples: `['-', 'get ready', 'Test']`
* **"5.1 SKOM Initiation__5.1.1 Confirm Site Id."** (VARCHAR)
    * Samples: `['-', 'BCD234', 'abc']`
* **"5.1 SKOM Initiation__5.1.2 Grid Category"** (VARCHAR)
    * Samples: `['-', 'Y1', 'Y2']`
* **"5.1 SKOM Initiation__5.1.3 Site Address"** (VARCHAR)
    * Samples: `['-', 'Makati City', 'hdjdkdsl']`
* **"5.3 Vendor Supervisor Validation__5.3.1 Vendor Supervisor Comments"** (VARCHAR)
    * Samples: `['-', 'bgdjkfoab', 'OK']`
* **"5.3 Vendor Supervisor Validation__5.3.2 Site Sketch"** (VARCHAR)
    * Samples: `['-', '1 attachment(s)']`
* **"5.3 Vendor Supervisor Validation__5.3.3 Vendor Supervisor Signature"** (VARCHAR)
    * Samples: `['-', 'Signed By: Joemar Rivera', 'Signed By: zjaneth-relucio micropowerph']`
* **"5.4 SKOM Validation__5.4.1 Comments"** (VARCHAR)
    * Samples: `['-', 'Ok', 'ok']`
* **"5.4 SKOM Validation__5.4.2 TSSR Escalation"** (VARCHAR)
    * Samples: `['-']`
* **"5.4 SKOM Validation__5.4.3 MPP Site Supervisor"** (VARCHAR)
    * Samples: `['-', 'Signed By: erick-acob micropowerph', 'Signed By: zjaneth-relucio micropowerph']`
* **"Are the TSSR issues resolved?"** (VARCHAR)
    * Samples: `['-']`
* **"5.5 SKOM Approval__5.5.1 Comments"** (VARCHAR)
    * Samples: `['-', 'ok', 'nynynr']`
* **"5.5 SKOM Approval__5.5.2 Regional I&C Manager Approval"** (VARCHAR)
    * Samples: `['-', 'Signed By: melchor-albino micropowerph', 'Signed By: ruel-bejo micropowerph']`
* **"6.1 MRN Preparation__6.1.2 MRN Date"** (VARCHAR)
    * Samples: `['-', '19-12-2024', '27-11-2024']`
* **"6.1 MRN Preparation__6.1.4 Customer"** (VARCHAR)
    * Samples: `['-', 'JJC', 'bth']`
* **"6.1 MRN Preparation__6.1.3 Region"** (VARCHAR)
    * Samples: `['-', 'MIN', 'slz']`
* **"6.1 MRN Preparation__6.1.5 Requested By"** (VARCHAR)
    * Samples: `['-', 'zbabylyn-ramos micropowerph', 'zkathrina-sanpedro micropowerph']`
* **"6.1 MRN Preparation__6.1.6 WH Team Assigned"** (VARCHAR)
    * Samples: `['-', 'zjaneth-relucio micropowerph', 'Joemar Rivera']`
* **"6.1 MRN Preparation__6.1.7 Dispatch Required By"** (VARCHAR)
    * Samples: `['-', '21-12-2024', '28-11-2024']`
* **"6.1 MRN Preparation__6.1.8 Special Instruction"** (VARCHAR)
    * Samples: `['-', 'gthrt', 'nrgnry']`
* **"6.3 MRN Approval__6.3.1 Approve MRN request from MPP Coordinator?"** (VARCHAR)
    * Samples: `['-', 'Yes']`
* **"6.4 Transport Information__6.4.1 Truck Start Odometer Reading"** (VARCHAR)
    * Samples: `['-', '12', '1231']`
* **"6.4 Transport Information__6.4.2 Truck Number"** (VARCHAR)
    * Samples: `['-', '2025', '123']`
* **"6.4 Transport Information__6.4.4 Driver Name"** (VARCHAR)
    * Samples: `['-', 'john', '233443']`
* **"6.4 Transport Information__6.4.3 Truck Type"** (VARCHAR)
    * Samples: `['-', '10 wheel', '123as']`
* **"6.4 Transport Information__6.4.5 Driver Phone"** (VARCHAR)
    * Samples: `['-', '63545415515', '639253545626']`
* **"6.4 Transport Information__6.4.6 Packaging List Doc. No."** (VARCHAR)
    * Samples: `['-', '5', 'gdhakjvdfgv']`
* **"6.5 Material Reception__6.5.1 Truck End Odometer Reading"** (VARCHAR)
    * Samples: `['-', '20', '134']`
* **"6.5 Material Reception__6.5.4 Vendor Name"** (VARCHAR)
    * Samples: `['-', 'JJC Infotech Solutions Co. (NLZ/SLZ)', 'ADCEM TELECONSTRUCT CORPORATION (NLZ/SLZ/VIS/MIN)']`
* **"6.5 Material Reception__6.5.3 Designation"** (VARCHAR)
    * Samples: `['-', 'Vendor Supervisor', 'hfjaj']`
* **"6.5 Material Reception__6.5.2 Received By"** (VARCHAR)
    * Samples: `['-', 'Joemar Rivera', 'hdjalb']`
* **"6.5 Material Reception__6.5.5 Receiver Phone"** (VARCHAR)
    * Samples: `['-', '639123123145', '639256455645']`
* **"8.1 Site Details__8.1.1 SubContractor Name"** (VARCHAR)
    * Samples: `['-', 'JJC Infotech Solutions Co. (NLZ/SLZ)', 'ADCEM TELECONSTRUCT CORPORATION (NLZ/SLZ/VIS/MIN)']`
* **"8.1 Site Details__8.1.4 Inverter System"** (VARCHAR)
    * Samples: `['-', 'SG10', 'string']`
* **"8.1 Site Details__8.1.2 Grid Category"** (VARCHAR)
    * Samples: `['-', 'Y1', 'Y2']`
* **"8.1 Site Details__8.1.3 Site Load"** (VARCHAR)
    * Samples: `['-', '10kW', '5kW']`
* **"8.1 Site Details__8.1.5 Pre-Commissioning Date"** (VARCHAR)
    * Samples: `['-', '19-12-2024', '28-11-2024']`
* **"8.1 Site Details__8.1.6 Guidelines"** (VARCHAR)
    * Samples: `['-', 'hghthy', 'gg']`
* **"8.3 Validation__8.3.1 Comments"** (VARCHAR)
    * Samples: `['-', 'Test']`
* **"8.3 Validation__8.3.2 Pre-Commissioned by"** (VARCHAR)
    * Samples: `['-', 'Signed By: Joemar Rivera', 'Signed By: zjaneth-relucio micropowerph']`
* **"9.0 Commissioning__9.1 Signed Commissioning Document"** (VARCHAR)
    * Samples: `['-', '1 attachment(s)']`
* **"9.0 Commissioning__9.2 Commissioning Date"** (VARCHAR)
    * Samples: `['-', '20-12-2024', '28-11-2024']`
* **"9.0 Commissioning__9.3 Comments"** (VARCHAR)
    * Samples: `['-', 'Test']`


* Table Name: forms_table_1
* **"ID"** (VARCHAR)
    * Samples: `['Ma4mJkeGVqE', 'FU7Ic2P0iZA', 'TvjMw4qUX9K']`
* **"Form name"** (VARCHAR)
    * Samples: `['VIS2021 - TALISAY', 'BCD234 I&C Workflow v4.0', 'ABC123 I&C Workflow v4.0']`
* **"5.2 SKOM Checklist__A. Checklist Item"** (VARCHAR)
    * Samples: `['Site Details', 'Lease Area', 'Project Plan']`
* **"5.2 SKOM Checklist__B. Description"** (VARCHAR)
    * Samples: `['Check and verify the site coordinates are same as per given site details', 'Site Location,Verify Site orientation with respect to North & South direction', 'Lease Area,Verify complete lease area and verify corner’s Latitude & Longitude']`
* **"5.2 SKOM Checklist__C. Status"** (VARCHAR)
    * Samples: `['OK', 'Not OK', 'NA']`


* Table Name: forms_table_2
* **"ID"** (VARCHAR)
    * Samples: `['Ma4mJkeGVqE', 'FU7Ic2P0iZA', 'TvjMw4qUX9K']`
* **"Form name"** (VARCHAR)
    * Samples: `['VIS2021 - TALISAY', 'BCD234 I&C Workflow v4.0', 'ABC123 I&C Workflow v4.0']`
* **"6.2 Item details__Item"** (VARCHAR)
    * Samples: `['PV-JA-595BN-B1-240087:Solar Module  JAM72D40 575-600 LB N-Type Bifacial', 'MMS-XG-BFA14-B1-240086-1:HPX-JQG-2500 Front bar 2500', 'MMS-XG-BFA14-B1-240086-2:HPX-LYP-2500 Ground screws']`
* **"6.2 Item details__UOM"** (VARCHAR)
    * Samples: `['nos', 'no', 'unit']`
* **"6.2 Item details__Requested Quantity"** (VARCHAR)
    * Samples: `['1', '45', '6']`
* **"6.2 Item details__Issued Quantity"** (VARCHAR)
    * Samples: `['-', '5', '1']`
* **"6.2 Item details__Received Quantity"** (VARCHAR)
    * Samples: `['-', '5', '1']`
* **"6.2 Item details__Remarks"** (VARCHAR)
    * Samples: `['-', 'aaa', 'a']`

* Table Name: forms_table_3
* **"ID"** (VARCHAR)
    * Samples: `['Ma4mJkeGVqE', 'FU7Ic2P0iZA', 'TvjMw4qUX9K']`
* **"Form name"** (VARCHAR)
    * Samples: `['VIS2021 - TALISAY', 'BCD234 I&C Workflow v4.0', 'ABC123 I&C Workflow v4.0']`
* **"7.1 Daily Log Sheet__Date"** (DATE)
    * Samples: `[01-01-2000, 12-01-2002, 11-02-2009]`
* **"7.1 Daily Log Sheet__Name"** (VARCHAR)
    * Samples: `['Kathrina', 'Joemar Rivera']`
* **"7.1 Daily Log Sheet__Company"** (VARCHAR)
    * Samples: `['MPP', 'ABD']`
* **"7.1 Daily Log Sheet__ID Number"** (INTEGER)
    * Samples: `[12344, 23453, 12342]`
* **"7.1 Daily Log Sheet__Time In"** (TIME)
    * Samples: `['10:14 am', '12:49 pm', '11:13 am']`
* **"7.1 Daily Log Sheet__Time Out"** (TIME)
    * Samples: `['10:14 am', '12:49 pm', '11:13 am']`
* **"7.1 Daily Log Sheet__Schedule Activity"** (VARCHAR)
    * Samples: `['ahaao', 'ground screw', 'Installation']`
* **"7.1 Daily Log Sheet__Signature"** (VARCHAR)
    * Samples: `['Signed By: zkathrina-sanpedro micropowerph', 'Signed By: Joemar Rivera']`